from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django import forms
from .models import Order, OrderItem, OrderStatusHistory, OrderCancellation, OrderReschedule


class OrderAdminForm(forms.ModelForm):
    """
    Custom form for Order admin with tax configuration options
    """
    # Add tax configuration selection
    tax_configuration = forms.ModelChoiceField(
        queryset=None,
        required=False,
        empty_label="Use Default Tax Configuration",
        help_text="Select specific tax configuration for this order (leave empty for default)"
    )

    # Add tax override options
    override_tax_calculation = forms.BooleanField(
        required=False,
        help_text="Check to manually override tax calculation"
    )

    manual_tax_amount = forms.DecimalField(
        max_digits=10,
        decimal_places=2,
        required=False,
        help_text="Manual tax amount (only if override is checked)"
    )

    tax_exemption_reason = forms.CharField(
        max_length=200,
        required=False,
        help_text="Reason for tax exemption or manual override"
    )

    class Meta:
        model = Order
        fields = '__all__'

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Populate tax configuration choices
        try:
            from taxation.models import TaxConfiguration
            self.fields['tax_configuration'].queryset = TaxConfiguration.objects.filter(is_active=True)
        except ImportError:
            # Remove field if taxation app is not available
            del self.fields['tax_configuration']

    def save(self, commit=True):
        order = super().save(commit=False)

        # Handle tax configuration override
        if self.cleaned_data.get('override_tax_calculation'):
            manual_tax = self.cleaned_data.get('manual_tax_amount', 0)
            if manual_tax:
                order.tax_amount = manual_tax
                order.total_amount = order.subtotal + manual_tax + order.discount_amount

        if commit:
            order.save()
        return order


class OrderItemInline(admin.TabularInline):
    """
    Inline admin for order items.
    """
    model = OrderItem
    extra = 0
    readonly_fields = ['total_price']
    fields = [
        'service', 'quantity', 'unit_price', 'discount_per_unit',
        'total_price', 'estimated_duration', 'special_instructions'
    ]


class OrderStatusHistoryInline(admin.TabularInline):
    """
    Inline admin for order status history.
    """
    model = OrderStatusHistory
    extra = 0
    readonly_fields = ['timestamp']
    fields = ['previous_status', 'new_status', 'changed_by', 'reason', 'timestamp']


@admin.register(Order)
class OrderAdmin(admin.ModelAdmin):
    """
    Admin interface for Order model with tax configuration support.
    """
    form = OrderAdminForm
    list_display = [
        'order_number', 'customer_info', 'status_badge', 'payment_status_badge',
        'total_amount', 'tax_amount', 'assigned_provider_info', 'scheduled_date', 'created_at'
    ]
    list_filter = [
        'status', 'payment_status', 'payment_method', 'created_at',
        'scheduled_date', 'assigned_provider'
    ]
    search_fields = [
        'order_number', 'customer__mobile', 'customer__email',
        'assigned_provider__mobile', 'coupon_code'
    ]
    readonly_fields = [
        'id', 'order_number', 'created_at', 'updated_at',
        'confirmed_at', 'completed_at', 'cancelled_at', 'get_tax_breakdown'
    ]
    inlines = [OrderItemInline, OrderStatusHistoryInline]
    
    fieldsets = (
        ('Order Information', {
            'fields': ('id', 'order_number', 'customer', 'status', 'created_at')
        }),
        ('Payment Details', {
            'fields': (
                'payment_status', 'payment_method', 'payment_id', 'payment_signature'
            )
        }),
        ('Financial Summary', {
            'fields': (
                'subtotal', 'tax_amount', 'discount_amount', 'minimum_order_fee',
                'total_amount', 'coupon_code', 'coupon_discount', 'get_tax_breakdown'
            )
        }),
        ('Tax Configuration', {
            'fields': (
                'tax_configuration', 'override_tax_calculation',
                'manual_tax_amount', 'tax_exemption_reason'
            ),
            'classes': ('collapse',),
            'description': 'Configure tax settings for this order. Staff can override default tax calculations here.'
        }),
        ('Service Details', {
            'fields': (
                'assigned_provider', 'scheduled_date', 'scheduled_time_slot',
                'delivery_address'
            )
        }),
        ('Notes', {
            'fields': ('customer_notes', 'admin_notes')
        }),
        ('Timestamps', {
            'fields': ('updated_at', 'confirmed_at', 'completed_at', 'cancelled_at'),
            'classes': ('collapse',)
        }),
    )
    
    def customer_info(self, obj):
        """Display customer information with link"""
        name = obj.customer.get_full_name() or obj.customer.mobile
        url = reverse('admin:authentication_user_change', args=[obj.customer.id])
        return format_html('<a href="{}">{}</a>', url, name)
    customer_info.short_description = 'Customer'
    
    def assigned_provider_info(self, obj):
        """Display assigned provider information"""
        if obj.assigned_provider:
            name = obj.assigned_provider.get_full_name() or obj.assigned_provider.mobile
            url = reverse('admin:authentication_user_change', args=[obj.assigned_provider.id])
            return format_html('<a href="{}">{}</a>', url, name)
        return "Not assigned"
    assigned_provider_info.short_description = 'Provider'
    
    def status_badge(self, obj):
        """Display status with color coding"""
        colors = {
            'pending': '#ffc107',
            'confirmed': '#17a2b8',
            'assigned': '#6f42c1',
            'in_progress': '#fd7e14',
            'completed': '#28a745',
            'cancelled': '#dc3545',
            'refunded': '#6c757d'
        }
        color = colors.get(obj.status, '#6c757d')
        return format_html(
            '<span style="background-color: {}; color: white; padding: 3px 8px; '
            'border-radius: 3px; font-size: 11px; font-weight: bold;">{}</span>',
            color, obj.get_status_display()
        )
    status_badge.short_description = 'Status'
    
    def payment_status_badge(self, obj):
        """Display payment status with color coding"""
        colors = {
            'pending': '#ffc107',
            'paid': '#28a745',
            'failed': '#dc3545',
            'refunded': '#6c757d'
        }
        color = colors.get(obj.payment_status, '#6c757d')
        return format_html(
            '<span style="background-color: {}; color: white; padding: 3px 8px; '
            'border-radius: 3px; font-size: 11px; font-weight: bold;">{}</span>',
            color, obj.get_payment_status_display()
        )
    payment_status_badge.short_description = 'Payment'

    def get_tax_breakdown(self, obj):
        """Display detailed tax breakdown for this order"""
        try:
            from taxation.models import TaxCalculation

            # Get the latest tax calculation for this order
            tax_calc = TaxCalculation.objects.filter(
                reference_type='order',
                reference_id=obj.id
            ).first()

            if tax_calc:
                breakdown = f"""
                <div style="background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace;">
                    <strong>Tax Breakdown:</strong><br>
                    Subtotal: ₹{tax_calc.subtotal}<br>
                    CGST: ₹{tax_calc.cgst_amount}<br>
                    SGST: ₹{tax_calc.sgst_amount}<br>
                    IGST: ₹{tax_calc.igst_amount}<br>
                    Service Charge: ₹{tax_calc.service_charge}<br>
                    <strong>Total Tax: ₹{tax_calc.total_tax}</strong><br>
                    <strong>Grand Total: ₹{tax_calc.total_amount}</strong>
                </div>
                """
                return format_html(breakdown)
            else:
                return format_html(
                    '<div style="color: #dc3545;">No tax calculation found for this order</div>'
                )
        except ImportError:
            return "Tax module not available"
    get_tax_breakdown.short_description = 'Tax Breakdown'


@admin.register(OrderItem)
class OrderItemAdmin(admin.ModelAdmin):
    """
    Admin interface for OrderItem model.
    """
    list_display = [
        'order_number', 'service_title', 'quantity', 'unit_price',
        'discount_per_unit', 'total_price', 'estimated_duration'
    ]
    list_filter = ['order__status', 'service_id']
    search_fields = [
        'order__order_number', 'service_title', 'order__customer__mobile'
    ]
    readonly_fields = ['total_price']

    def order_number(self, obj):
        return obj.order.order_number
    order_number.short_description = 'Order Number'


@admin.register(OrderStatusHistory)
class OrderStatusHistoryAdmin(admin.ModelAdmin):
    """
    Admin interface for OrderStatusHistory model.
    """
    list_display = [
        'order_number', 'previous_status', 'new_status',
        'changed_by_info', 'timestamp'
    ]
    list_filter = ['new_status', 'previous_status', 'timestamp']
    search_fields = ['order__order_number', 'changed_by__mobile', 'reason']
    readonly_fields = ['timestamp']
    
    def order_number(self, obj):
        return obj.order.order_number
    order_number.short_description = 'Order Number'
    
    def changed_by_info(self, obj):
        if obj.changed_by:
            return obj.changed_by.get_full_name() or obj.changed_by.mobile
        return "System"
    changed_by_info.short_description = 'Changed By'


@admin.register(OrderCancellation)
class OrderCancellationAdmin(admin.ModelAdmin):
    """
    Admin interface for OrderCancellation model.
    """
    list_display = [
        'order_number', 'reason', 'cancelled_by_info',
        'refund_amount', 'refund_processed', 'cancelled_at'
    ]
    list_filter = ['reason', 'refund_processed', 'cancelled_at']
    search_fields = ['order__order_number', 'cancelled_by__mobile', 'description']
    readonly_fields = ['cancelled_at']
    
    def order_number(self, obj):
        return obj.order.order_number
    order_number.short_description = 'Order Number'
    
    def cancelled_by_info(self, obj):
        if obj.cancelled_by:
            return obj.cancelled_by.get_full_name() or obj.cancelled_by.mobile
        return "System"
    cancelled_by_info.short_description = 'Cancelled By'


@admin.register(OrderReschedule)
class OrderRescheduleAdmin(admin.ModelAdmin):
    """
    Admin interface for OrderReschedule model.
    """
    list_display = [
        'order_number', 'original_date', 'new_date',
        'requested_by_info', 'approved', 'approved_by_info', 'created_at'
    ]
    list_filter = ['approved', 'original_date', 'new_date', 'created_at']
    search_fields = ['order__order_number', 'requested_by__mobile', 'reason']
    readonly_fields = ['created_at']
    
    def order_number(self, obj):
        return obj.order.order_number
    order_number.short_description = 'Order Number'
    
    def requested_by_info(self, obj):
        if obj.requested_by:
            return obj.requested_by.get_full_name() or obj.requested_by.mobile
        return "Unknown"
    requested_by_info.short_description = 'Requested By'
    
    def approved_by_info(self, obj):
        if obj.approved_by:
            return obj.approved_by.get_full_name() or obj.approved_by.mobile
        return "Not approved"
    approved_by_info.short_description = 'Approved By'


# Customize admin site headers
admin.site.site_header = "Home Services - Order Management"
admin.site.site_title = "Order Admin"
admin.site.index_title = "Order & Booking Management"
